/**
 * Exportações centralizadas dos templates de e-mail
 * Facilita a importação e uso dos templates em outros módulos
 */

// Template base e componentes auxiliares
export {
  BaseEmailTemplate,
  EmailButton,
  EmailText,
  EmailHeading,
  EmailDivider,
  type BaseEmailTemplateProps,
  type EmailButtonProps,
  type EmailTextProps,
  type EmailHeadingProps
} from './base-email-template';

// Template de lembrete de aula
export {
  ClassReminderTemplate,
  type ClassReminderTemplateProps
} from './class-reminder-template';

// Template de lembrete de pagamento de mensalidade
export {
  PaymentReminderTemplate,
  type PaymentReminderTemplateProps
} from './payment-reminder-template';

// Template de vencimento de pagamento de matrícula
export {
  EnrollmentPaymentDueTemplate,
  type EnrollmentPaymentDueTemplateProps
} from './enrollment-payment-due-template';

// Template de vencimento de despesas (para dono da academia)
export {
  ExpenseDueTemplate,
  type ExpenseDueTemplateProps
} from './expense-due-template';

// Template de notificação de nova matrícula (para dono da academia)
export {
  NewEnrollmentNotificationTemplate,
  type NewEnrollmentNotificationTemplateProps
} from './new-enrollment-notification-template';

// Template padrão do sistema
export {
  SystemDefaultTemplate,
  type SystemDefaultTemplateProps
} from './system-default-template';

// Tipos de templates disponíveis
export type EmailTemplateType = 
  | 'class-reminder'
  | 'payment-reminder'
  | 'enrollment-payment-due'
  | 'expense-due'
  | 'new-enrollment-notification'
  | 'system-default';

// Mapeamento de tipos para componentes
export const EMAIL_TEMPLATES = {
  'class-reminder': ClassReminderTemplate,
  'payment-reminder': PaymentReminderTemplate,
  'enrollment-payment-due': EnrollmentPaymentDueTemplate,
  'expense-due': ExpenseDueTemplate,
  'new-enrollment-notification': NewEnrollmentNotificationTemplate,
  'system-default': SystemDefaultTemplate
} as const;

// Props unificadas para todos os templates
export type EmailTemplateProps = 
  | ClassReminderTemplateProps
  | PaymentReminderTemplateProps
  | EnrollmentPaymentDueTemplateProps
  | ExpenseDueTemplateProps
  | NewEnrollmentNotificationTemplateProps
  | SystemDefaultTemplateProps;

// Metadados dos templates
export interface EmailTemplateMetadata {
  id: EmailTemplateType;
  name: string;
  description: string;
  category: 'student' | 'owner' | 'system';
  requiredProps: string[];
}

export const EMAIL_TEMPLATE_METADATA: Record<EmailTemplateType, EmailTemplateMetadata> = {
  'class-reminder': {
    id: 'class-reminder',
    name: 'Lembrete de Aula',
    description: 'Notifica alunos sobre aulas próximas',
    category: 'student',
    requiredProps: ['academyName', 'studentName', 'className', 'classDate', 'classTime']
  },
  'payment-reminder': {
    id: 'payment-reminder',
    name: 'Lembrete de Pagamento',
    description: 'Lembra alunos sobre mensalidades vencendo ou em atraso',
    category: 'student',
    requiredProps: ['academyName', 'studentName', 'amount', 'dueDate', 'planName']
  },
  'enrollment-payment-due': {
    id: 'enrollment-payment-due',
    name: 'Vencimento de Matrícula',
    description: 'Notifica sobre vencimento de pagamento de matrícula',
    category: 'student',
    requiredProps: ['academyName', 'studentName', 'amount', 'dueDate', 'enrollmentDate', 'planName']
  },
  'expense-due': {
    id: 'expense-due',
    name: 'Vencimento de Despesas',
    description: 'Notifica o dono sobre despesas da academia vencendo',
    category: 'owner',
    requiredProps: ['academyName', 'ownerName', 'expenseDescription', 'amount', 'dueDate', 'category']
  },
  'new-enrollment-notification': {
    id: 'new-enrollment-notification',
    name: 'Nova Matrícula',
    description: 'Notifica o dono sobre novas matrículas realizadas',
    category: 'owner',
    requiredProps: ['academyName', 'ownerName', 'studentName', 'studentEmail', 'planName', 'enrollmentDate', 'startDate', 'amount', 'paymentStatus']
  },
  'system-default': {
    id: 'system-default',
    name: 'Template Padrão',
    description: 'Template genérico para notificações do sistema',
    category: 'system',
    requiredProps: ['academyName', 'title', 'message']
  }
};

// Função auxiliar para obter template por tipo
export function getEmailTemplate(type: EmailTemplateType) {
  return EMAIL_TEMPLATES[type];
}

// Função auxiliar para obter metadados por tipo
export function getEmailTemplateMetadata(type: EmailTemplateType) {
  return EMAIL_TEMPLATE_METADATA[type];
}

// Função para validar se as props necessárias estão presentes
export function validateTemplateProps(type: EmailTemplateType, props: any): boolean {
  const metadata = getEmailTemplateMetadata(type);
  return metadata.requiredProps.every(prop => props[prop] !== undefined);
}
